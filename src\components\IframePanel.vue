<template>
  <div class="iframe-panel" :class="{ collapsed: isCollapsed }">
    <div class="panel-header" v-if="title && title.length > 0">
      <h3 class="panel-title">{{ title }}</h3>
      <div class="panel-actions">
        <el-button
          size="small"
          type="primary"
          link
          @click="refreshIframe"
          :loading="refreshing"
        >
          <el-icon><Refresh /></el-icon>
        </el-button>
        <el-button
          v-if="collapsible"
          size="small"
          type="primary"
          link
          @click="toggleCollapse"
        >
          <el-icon>
            <component :is="isCollapsed ? 'ArrowDown' : 'ArrowUp'" />
          </el-icon>
        </el-button>
      </div>
    </div>

    <div class="panel-content" v-show="!isCollapsed">
      <div v-if="loading" class="loading-overlay">
        <p>加载中...</p>
      </div>

      <div v-if="error" class="error-overlay">
        <p>{{ error }}</p>
        <el-button size="small" @click="retryLoad">重试</el-button>
      </div>

      <div v-if="isDemo" class="demo-content">
        <div class="demo-title">{{ title }}</div>
        <div class="demo-data">{{ demoData }}</div>
        <div class="demo-unit">%RH</div>
        <div class="demo-status">正常运行</div>
      </div>

      <iframe
        v-show="!loading && !error && !isDemo"
        ref="iframeRef"
        :src="url"
        :title="title"
        class="panel-iframe"
        frameborder="0"
        allowfullscreen
        @load="handleLoad"
        @error="handleError"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Refresh, ArrowUp, ArrowDown } from "@element-plus/icons-vue";

const props = defineProps({
  url: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    default: "iframe面板",
  },
  collapsible: {
    type: Boolean,
    default: false,
  },
});

const iframeRef = ref();
const loading = ref(false);
const error = ref("");
const refreshing = ref(false);
const demoData = ref(Math.floor(Math.random() * 100));
const isCollapsed = ref(false);

const isDemo = computed(() => {
  return props.url.includes("example.com");
});

const handleLoad = () => {
  loading.value = false;
  error.value = "";
};

const handleError = () => {
  loading.value = false;
  error.value = "加载失败，请检查网络连接或URL是否正确";
};

const refreshIframe = async () => {
  refreshing.value = true;

  if (isDemo.value) {
    demoData.value = Math.floor(Math.random() * 100);
  } else {
    loading.value = true;
    error.value = "";

    if (iframeRef.value) {
      iframeRef.value.src = iframeRef.value.src;
    }
  }

  setTimeout(() => {
    refreshing.value = false;
    loading.value = false;
  }, 500);
};

const retryLoad = () => {
  error.value = "";
  loading.value = true;
  refreshIframe();
};

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

onMounted(() => {
  if (isDemo.value) {
    setInterval(() => {
      demoData.value = Math.floor(Math.random() * 100);
    }, 3000);
  }
});
</script>

<style scoped>
.iframe-panel {
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  transition: height 0.3s ease;
}

.iframe-panel.collapsed {
  height: auto;
  min-height: 40px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 162, 255, 0.1);
  border-bottom: 1px solid rgba(0, 162, 255, 0.2);
}

.panel-title {
  font-size: 14px;
  font-weight: 500;
  color: #00a2ff;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.panel-actions {
  display: flex;
  gap: 5px;
}

.panel-content {
  position: relative;
  width: 100%;
  height: calc(100% - 0px);
}

.panel-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  z-index: 10;
}

.demo-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
}

.demo-title {
  font-size: 18px;
  /* margin-bottom: 15px; */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.demo-data {
  font-size: 36px;
  font-weight: bold;
  margin: 10px 0;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  transition: all 0.3s ease;
}

.demo-unit {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 15px;
}

.demo-status {
  padding: 8px 16px;
  background: rgba(0, 255, 136, 0.2);
  border-radius: 15px;
  border: 1px solid rgba(0, 255, 136, 0.3);
  font-size: 12px;
}
</style>
