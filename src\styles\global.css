/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  background: #0f1419;
  color: #ffffff;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
}

/* 蓝色科技风格主题色彩 */
:root {
  --primary-color: #00a2ff;
  --primary-light: #33b5ff;
  --primary-dark: #0088cc;
  --secondary-color: #1a2332;
  --background-dark: #0c1426;
  --background-light: #1a2332;
  --text-primary: #ffffff;
  --text-secondary: #8892b0;
  --border-color: rgba(0, 162, 255, 0.3);
  --shadow-color: rgba(0, 162, 255, 0.2);
  --success-color: #00ff88;
  --warning-color: #ffaa00;
  --error-color: #ff4757;
}

/* Element Plus 主题定制 */
.el-button--primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: #ffffff;
  transition: all 0.3s ease;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.el-button--primary:active {
  transform: translateY(0);
}

.el-input__wrapper {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.el-input__wrapper:hover {
  border-color: rgba(0, 162, 255, 0.6) !important;
}

.el-input__wrapper.is-focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 2px var(--shadow-color) !important;
}

.el-input__inner {
  color: var(--text-primary) !important;
  background: transparent !important;
}

.el-input__inner::placeholder {
  color: var(--text-secondary) !important;
}

.el-form-item__error {
  color: var(--error-color) !important;
}

.el-message {
  background: rgba(15, 20, 25, 0.95) !important;
  border: 1px solid var(--border-color) !important;
  backdrop-filter: blur(10px) !important;
}

.el-message--success {
  border-color: rgba(0, 255, 136, 0.3) !important;
}

.el-message--error {
  border-color: rgba(255, 71, 87, 0.3) !important;
}

.el-message--warning {
  border-color: rgba(255, 170, 0, 0.3) !important;
}

.el-message-box {
  background: rgba(15, 20, 25, 0.95) !important;
  border: 1px solid var(--border-color) !important;
  backdrop-filter: blur(10px) !important;
}

.el-message-box__header {
  border-bottom: 1px solid var(--border-color) !important;
}

.el-message-box__title {
  color: var(--text-primary) !important;
}

.el-message-box__content {
  color: var(--text-secondary) !important;
}

.el-overlay {
  background: rgba(0, 0, 0, 0.7) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
}

/* 科技感动画效果 */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color), 0 0 15px var(--primary-color);
  }
  50% {
    box-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes slideInFromTop {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 工具类 */
.tech-border {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.tech-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.tech-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.slide-in-top {
  animation: slideInFromTop 0.6s ease-out;
}

.slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* 响应式断点 */
@media (max-width: 1400px) {
  .dashboard-content {
    gap: 10px;
  }
}

@media (max-width: 1200px) {
  .dashboard-content {
    gap: 8px;
  }
  
  .panel-title {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 10px;
  }
  
  .left-panels,
  .right-panels {
    grid-column: 1;
    flex-direction: row;
    height: auto;
  }
  
  .bottom-cameras {
    grid-column: 1;
  }
}
