<template>
  <div class="test-page-container">
    <!-- 页面标题 -->
    <div class="test-header">
      <h1 class="test-title">MonitorChart 组件测试页面</h1>
      <div class="test-controls">
        <el-button @click="refreshCharts" type="primary" size="small">
          <el-icon><Refresh /></el-icon>
          刷新图表
        </el-button>
        <el-button @click="goBack" type="info" size="small">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <!-- 测试配置面板 -->
    <div class="test-config">
      <el-card class="config-card">
        <template #header>
          <span>测试配置</span>
        </template>
        <div class="config-content">
          <div class="config-item">
            <label>数据类型:</label>
            <el-select v-model="testConfig.dataType" @change="updateCharts">
              <el-option label="湿度 (humidity)" value="humidity" />
              <el-option label="压力 (pressure)" value="pressure" />
              <el-option label="温度 (temperature)" value="temperature" />
              <el-option label="流量 (flow)" value="flow" />
            </el-select>
          </div>
          <div class="config-item">
            <label>点位ID:</label>
            <el-select v-model="testConfig.id" @change="updateCharts">
              <el-option label="点位1 - Exhaust Sleeve（No. 49）" value="1" />
              <el-option label="点位2 - Injection Sleeve（No. 57）" value="2" />
              <el-option label="点位3 - Plant Room（No. 61）" value="3" />
              <el-option label="点位4 - Exhaust Sleeve（No. 66）" value="4" />
            </el-select>
          </div>
          <div class="config-item">
            <label>图表高度:</label>
            <el-input-number 
              v-model="testConfig.chartHeight" 
              :min="200" 
              :max="600" 
              :step="50"
              @change="updateCharts"
            />
          </div>
          <div class="config-item">
            <label>实时更新:</label>
            <el-switch v-model="testConfig.realTimeEnabled" @change="updateCharts" />
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表测试区域 -->
    <div class="test-charts">
      <!-- 单个图表测试 -->
      <div class="chart-section">
        <h3 class="section-title">单个图表测试</h3>
        <div class="chart-container">
          <MonitorChart
            :key="chartKey"
            :title="getChartTitle()"
            :data-type="testConfig.dataType"
            :id="testConfig.id"
            :chart-height="testConfig.chartHeight"
            :real-time-enabled="testConfig.realTimeEnabled"
            class="test-chart"
          />
        </div>
      </div>

      <!-- 多个图表对比测试 -->
      <!-- <div class="chart-section">
        <h3 class="section-title">多图表对比测试</h3>
        <div class="charts-grid">
          <MonitorChart
            :key="`humidity-air-${chartKey}`"
            title="湿度 - 进气传感器"
            data-type="humidity"
            sensor-type="air"
            :chart-height="250"
            :real-time-enabled="true"
            class="grid-chart"
          />
          <MonitorChart
            :key="`humidity-exhaust-${chartKey}`"
            title="湿度 - 排气传感器"
            data-type="humidity"
            sensor-type="exhaust"
            :chart-height="250"
            :real-time-enabled="true"
            class="grid-chart"
          />
          <MonitorChart
            :key="`pressure-air-${chartKey}`"
            title="压力 - 进气传感器"
            data-type="pressure"
            sensor-type="air"
            :chart-height="250"
            :real-time-enabled="true"
            class="grid-chart"
          />
          <MonitorChart
            :key="`pressure-exhaust-${chartKey}`"
            title="压力 - 排气传感器"
            data-type="pressure"
            sensor-type="exhaust"
            :chart-height="250"
            :real-time-enabled="true"
            class="grid-chart"
          />
        </div>
      </div> -->

      <!-- 不同尺寸测试 -->
      <!-- <div class="chart-section">
        <h3 class="section-title">不同尺寸测试</h3>
        <div class="size-test-grid">
          <MonitorChart
            :key="`small-${chartKey}`"
            title="小尺寸图表 (200px)"
            data-type="humidity"
            sensor-type="air"
            :chart-height="200"
            :real-time-enabled="false"
            class="size-chart small"
          />
          <MonitorChart
            :key="`medium-${chartKey}`"
            title="中等尺寸图表 (300px)"
            data-type="pressure"
            sensor-type="exhaust"
            :chart-height="300"
            :real-time-enabled="false"
            class="size-chart medium"
          />
          <MonitorChart
            :key="`large-${chartKey}`"
            title="大尺寸图表 (400px)"
            data-type="humidity"
            sensor-type="exhaust"
            :chart-height="400"
            :real-time-enabled="false"
            class="size-chart large"
          />
        </div>
      </div> -->
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
      <el-card class="debug-card">
        <template #header>
          <span>调试信息</span>
        </template>
        <div class="debug-content">
          <p><strong>当前配置:</strong></p>
          <pre>{{ JSON.stringify(testConfig, null, 2) }}</pre>
          <p><strong>图表标题:</strong> {{ getChartTitle() }}</p>
          <p><strong>更新时间:</strong> {{ lastUpdateTime }}</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, ArrowLeft } from '@element-plus/icons-vue'
import MonitorChart from '@/components/MonitorChart.vue'

const router = useRouter()

// 测试配置
const testConfig = ref({
  dataType: 'humidity',
  id: '1',
  chartHeight: 300,
  realTimeEnabled: true
})

// 图表刷新键
const chartKey = ref(0)
const lastUpdateTime = ref('')

// 获取图表标题
const getChartTitle = () => {
  const pointMap = {
    '1': 'Exhaust Sleeve（No. 49）',
    '2': 'Injection Sleeve（No. 57）',
    '3': 'Plant Room（No. 61）',
    '4': 'Exhaust Sleeve（No. 66）'
  }
  const dataMap = {
    humidity: '湿度',
    pressure: '压力',
    temperature: '温度',
    flow: '流量'
  }
  return `${pointMap[testConfig.value.id]} - ${dataMap[testConfig.value.dataType]}监测图表`
}

// 更新图表
const updateCharts = () => {
  chartKey.value++
  lastUpdateTime.value = new Date().toLocaleString()
  ElMessage.success('图表配置已更新')
}

// 刷新图表
const refreshCharts = () => {
  chartKey.value++
  lastUpdateTime.value = new Date().toLocaleString()
  ElMessage.success('图表已刷新')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  lastUpdateTime.value = new Date().toLocaleString()
  ElMessage.info('MonitorChart 测试页面已加载')
})
</script>

<style scoped>
.test-page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  padding: 20px;
  color: #ffffff;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
}

.test-title {
  color: #00a2ff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
}

.test-controls {
  display: flex;
  gap: 10px;
}

.test-config {
  margin-bottom: 30px;
}

.config-card {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
}

.config-content {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: center;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  color: #ffffff;
  font-weight: 500;
  min-width: 80px;
}

.test-charts {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.chart-section {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
}

.section-title {
  color: #00a2ff;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 20px 0;
  text-shadow: 0 0 5px rgba(0, 162, 255, 0.3);
}

.chart-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.test-chart {
  width: 100%;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.grid-chart {
  width: 100%;
}

.size-test-grid {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.size-chart {
  flex-shrink: 0;
}

.size-chart.small {
  width: 300px;
}

.size-chart.medium {
  width: 400px;
}

.size-chart.large {
  width: 500px;
}

.debug-info {
  margin-top: 30px;
}

.debug-card {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
}

.debug-content {
  color: #ffffff;
}

.debug-content pre {
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 4px;
  border: 1px solid rgba(0, 162, 255, 0.2);
  color: #00a2ff;
  font-size: 12px;
  overflow-x: auto;
}

/* Element Plus 样式覆盖 */
:deep(.el-card__header) {
  background: rgba(0, 162, 255, 0.1);
  border-bottom: 1px solid rgba(0, 162, 255, 0.2);
  color: #00a2ff;
  font-weight: 600;
}

:deep(.el-card__body) {
  background: transparent;
}

:deep(.el-select) {
  width: 150px;
}

:deep(.el-select .el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 162, 255, 0.3);
  color: #ffffff;
}

:deep(.el-input-number .el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 162, 255, 0.3);
  color: #ffffff;
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #00a2ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
  
  .size-test-grid {
    flex-direction: column;
    align-items: center;
  }
  
  .size-chart.small,
  .size-chart.medium,
  .size-chart.large {
    width: 100%;
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .test-page-container {
    padding: 10px;
  }
  
  .test-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .config-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .config-item {
    justify-content: space-between;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
