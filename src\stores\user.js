import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, getUserInfo as apiGetUserInfo } from '@/utils/dataService'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const refreshToken = ref(localStorage.getItem('refreshToken') || '')

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)

  // 登录方法
  const login = async (username, password) => {
    try {
      const response = await apiLogin(username, password)

      if (response.success) {
        token.value = response.data.token
        refreshToken.value = response.data.refreshToken || ''
        userInfo.value = response.data.userInfo

        // 保存到本地存储
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('refreshToken', response.data.refreshToken || '')
        localStorage.setItem('userInfo', JSON.stringify(response.data.userInfo))

        return { success: true, message: response.message || '登录成功' }
      } else {
        return { success: false, message: response.message || '登录失败' }
      }
    } catch (error) {
      console.error('登录错误:', error)
      return { success: false, message: error.message || '登录失败，请重试' }
    }
  }

  // 登出方法
  const logout = () => {
    token.value = ''
    refreshToken.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('userInfo')
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      if (!token.value) {
        return { success: false, message: '未登录' }
      }

      const response = await apiGetUserInfo()

      if (response.success) {
        userInfo.value = response.data
        localStorage.setItem('userInfo', JSON.stringify(response.data))
        return { success: true, message: '获取用户信息成功' }
      } else {
        return { success: false, message: response.message || '获取用户信息失败' }
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      return { success: false, message: error.message || '获取用户信息失败' }
    }
  }

  // 初始化用户信息
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    const savedRefreshToken = localStorage.getItem('refreshToken')

    if (savedRefreshToken) {
      refreshToken.value = savedRefreshToken
    }

    if (savedUserInfo && token.value) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    userInfo,
    token,
    refreshToken,
    isAuthenticated,
    login,
    logout,
    fetchUserInfo,
    initUserInfo
  }
})
