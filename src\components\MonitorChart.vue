<template>
  <div class="monitor-chart-container">
    <div class="chart-header">
      <!-- 标题行 -->
      <div class="chart-title-row">
        <h3 class="chart-title">{{ dynamicTitle }}</h3>
      </div>

      <!-- 功能控制行 -->
      <div class="chart-controls-row">
        <div class="chart-left-controls">
          <div class="control-group">
            <el-select
              v-model="currentId"
              @change="handleIdChange"
              size="small"
              style="width: 120px"
            >
              <el-option label="No. 49 - Exhaust Sleeve" value="1" />
              <el-option label="No. 57 - Injection Sleeve" value="2" />
              <el-option label="No. 61 - Plant Room" value="3" />
              <el-option label="No. 66 - Exhaust Sleeve" value="4" />
            </el-select>
          </div>
          <div class="control-group">
            <el-select
              v-model="currentDataType"
              @change="handleDataTypeChange"
              size="small"
              style="width: 40px"
            >
              <el-option label="humidity" value="humidity" />
              <el-option label="pressure" value="pressure" />
              <el-option label="temperature" value="temperature" />
              <el-option label="flow" value="flow" />
            </el-select>
          </div>
        </div>

        <div class="chart-right-controls">
          <el-select
            v-model="timeRange"
            @change="handleTimeRangeChange"
            size="small"
            style="width: 40px"
          >
            <el-option label="1h" value="1h" />
            <el-option label="6h" value="6h" />
            <el-option label="12h" value="12h" />
            <el-option label="24h" value="24h" />
            <el-option label="7D" value="7d" />
          </el-select>
          <el-button
            @click="toggleRealTime"
            size="small"
            :type="isRealTime ? 'warning' : 'primary'"
          >
            {{ isRealTime ? "pause" : "recover" }}
          </el-button>
        </div>
      </div>
    </div>

    <div class="chart-content">
      <div
        ref="chartRef"
        class="chart-wrapper"
        :style="{ height: chartHeight + 'px' }"
      ></div>

      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>Loading...</span>
      </div>

      <div v-if="error" class="chart-error">
        <el-icon><Warning /></el-icon>
        <span>{{ error }}</span>
        <el-button @click="retryLoad" size="small" type="primary"
          >retry</el-button
        >
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from "vue";
import * as echarts from "echarts";
import { getHistoryData } from "@/utils/dataService";
import { ElMessage } from "element-plus";
import { Loading, Warning } from "@element-plus/icons-vue";
import { APP_CONFIG } from "@/utils/config";

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  dataType: {
    type: String,
    required: true,
    validator: (value) => ["humidity", "pressure", "temperature", "flow"].includes(value),
  },
  id: {
    type: [String, Number],
    required: true,
  },
  chartHeight: {
    type: Number,
    default: 300,
  },
  realTimeEnabled: {
    type: Boolean,
    default: true,
  },
});

const chartRef = ref(null);
const chart = ref(null);
const loading = ref(false);
const error = ref("");
const timeRange = ref("24h");
const isRealTime = ref(props.realTimeEnabled);
const chartData = ref([]);
// 内部状态，用于下拉框控制
const currentDataType = ref(props.dataType);
const currentId = ref(props.id);
let realTimeTimer = null;
let resizeTimer = null;

// 记录初始状态，用于判断是否发生了切换
const initialId = ref(props.id);
const initialDataType = ref(props.dataType);

// 动态标题计算属性
const dynamicTitle = computed(() => {
  // 如果有 prop title 且 id 和 dataType 都没有发生变化，优先显示 prop title
  if (props.title &&
      currentId.value === initialId.value &&
      currentDataType.value === initialDataType.value) {
    return props.title;
  }

  // 查找对应的监测点位配置
  const monitorPoint = APP_CONFIG.MONITOR_POINTS.find(point => point.id === currentId.value);
  if (!monitorPoint) {
    return props.title || "监测图表";
  }

  return `${monitorPoint.title}`;
});

// 安全的图表初始化
const initChart = () => {
  if (!chartRef.value) {
    console.warn("图表容器未准备好");
    return;
  }

  try {
    // 确保容器有有效的尺寸
    if (chartRef.value.offsetWidth === 0 || chartRef.value.offsetHeight === 0) {
      console.warn("图表容器尺寸为0，延迟初始化");
      setTimeout(() => {
        if (
          chartRef.value &&
          chartRef.value.offsetWidth > 0 &&
          chartRef.value.offsetHeight > 0
        ) {
          initChartSafely();
        }
      }, 200);
      return;
    }

    initChartSafely();
  } catch (error) {
    console.error("初始化图表失败:", error);
  }
};

// 安全的图表初始化核心方法
const initChartSafely = () => {
  try {
    // 如果已有图表实例，先销毁
    if (chart.value) {
      chart.value.dispose();
      chart.value = null;
    }

    // 创建新的图表实例
    chart.value = echarts.init(chartRef.value);

    if (!chart.value) {
      console.error("ECharts实例创建失败");
      return;
    }

    // 获取完整的图表配置
    const option = getChartOption();

    // 确保配置完整性
    if (!option || !option.series || !Array.isArray(option.series)) {
      console.error("图表配置不完整");
      return;
    }

    // 设置图表配置
    chart.value.setOption(option, true); // 第二个参数为true，表示不合并配置

    // 添加resize监听器（只添加一次）
    window.removeEventListener("resize", handleResize); // 先移除避免重复
    window.addEventListener("resize", handleResize);

  } catch (error) {
    console.error("安全初始化图表失败:", error);
    if (chart.value) {
      try {
        chart.value.dispose();
      } catch (disposeError) {
        console.error("销毁图表失败:", disposeError);
      }
      chart.value = null;
    }
  }
};

// 获取安全的图表配置
const getChartOption = () => {
  try {
    const seriesName = getSeriesName();
    const lineColor = getLineColor();

    // 确保所有必需的值都存在
    if (!seriesName || !lineColor) {
      console.warn("图表配置参数不完整");
      return null;
    }

    return {
      backgroundColor: "transparent",
      title: {
        show: false,
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        borderColor: "#00a2ff",
        borderWidth: 1,
        textStyle: {
          color: "#ffffff",
        },
        formatter: function (params) {
          if (!params || !params[0]) return "";
          const data = params[0];
          const time = new Date(data.axisValue).toLocaleString();
          const value = data.value;
          const unitMap = {
            humidity: "%",
            pressure: "pa",
            temperature: "℃",
            flow: "m³/h",
          };
          const unit = unitMap[currentDataType.value] || "";
          return `${time}<br/>${data.seriesName}: ${value}${unit}`;
        },
      },
      legend: {
        show: false,
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "8%",
        top: "8%",
        containLabel: true,
      },
      xAxis: {
        type: "time",
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: "#00a2ff",
          },
        },
        axisLabel: {
          color: "#ffffff",
          fontSize: 10,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "rgba(0, 162, 255, 0.1)",
          },
        },
      },
      yAxis: getYAxisConfig(),
      series: [
        {
          name: seriesName,
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 4,
          lineStyle: {
            color: lineColor,
            width: 2,
          },
          itemStyle: {
            color: lineColor,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: lineColor + "40" },
              { offset: 1, color: lineColor + "10" },
            ]),
          },
          data: [], // 初始为空数组
        },
      ],
    };
  } catch (error) {
    console.error("生成图表配置失败:", error);
    return null;
  }
};

// 获取系列名称
const getSeriesName = () => {
  const dataMap = {
    humidity: "湿度",
    pressure: "压力",
    temperature: "温度",
    flow: "流量",
  };
  return `${dataMap[currentDataType.value]}`;
};

// 获取线条颜色
const getLineColor = () => {
  const colorMap = {
    humidity: "#00a2ff",
    pressure: "#ff6b35",
    temperature: "#00ff88",
    flow: "#ffd700",
  };
  return colorMap[currentDataType.value] || "#00a2ff";
};

// 获取Y轴配置
const getYAxisConfig = () => {
  const baseConfig = {
    type: "value",
    axisLine: {
      lineStyle: {
        color: "#00a2ff",
      },
    },
    axisLabel: {
      color: "#ffffff",
      fontSize: 10,
      formatter: function (value) {
        const unitMap = {
          humidity: "%",
          pressure: "pa",
          temperature: "℃",
          flow: "m³/h",
        };
        const unit = unitMap[currentDataType.value] || "";
        return `${value}${unit}`;
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: "rgba(0, 162, 255, 0.1)",
      },
    },
  };

  // 根据数据类型设置特定的Y轴范围
  if (['pressure','flow','temperature','humidity'].includes(currentDataType.value)) {
    return {
      ...baseConfig,
      min: function(value) {
        // 压力数据通常在2200左右，变化范围很小
        // 为了更好地展示趋势，设置一个合适的起始值
        const dataMin = value.min;
        const dataMax = value.max;
        const range = dataMax - dataMin;

        
        // 如果数据范围很小，扩展显示范围以更好地展示变化
        if (range < 20) {
          // return Math.floor(dataMin - 10);
          return Math.floor(dataMin / 10) * 10;
        } else {
          return Math.floor(dataMin - range * 0.1);
        }
      },
      max: function(value) {
        // 设置最大值，确保有足够的显示空间
        const dataMin = value.min;
        const dataMax = value.max;
        const range = dataMax - dataMin;

        if (range < 20) {
          // return Math.ceil(dataMax + 10);
           return Math.ceil(dataMax / 10) * 10+5;
        } else {
          return Math.ceil(dataMax + range * 0.1);
        }
      },
      scale: true, // 启用自动缩放，更好地展示数据趋势
    };
  } 
  // else if (currentDataType.value === "humidity") {
  //   return {
  //     ...baseConfig,
  //     min: 0,
  //     max: 100, // 湿度范围0-100%
  //     scale: false,
  //   };
  // } 
  // else if (currentDataType.value === "temperature") {
  //   return {
  //     ...baseConfig,
  //     min: 0,
  //     max: 50, // 温度范围0-50℃
  //     scale: false,
  //   };
  // } 
  // else if (currentDataType.value === "flow") {
  //   return {
  //     ...baseConfig,
  //     min: 0,
  //     scale: true, // 流量启用自动缩放
  //   };
  // } 
  else {
    return {
      ...baseConfig,
      scale: true, // 其他数据类型启用自动缩放
    };
  }
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  error.value = "";

  try {
    // 使用新的参数格式
    const response = await getHistoryData(
      currentId.value, // siteId
      currentDataType.value, // dataType
      timeRange.value // period
    );

    // 处理新的响应格式
    if (response.data && response.data.dataList) {
      // 转换数据格式以适配图表
      chartData.value = response.data.dataList.map(item => ({
        time: item.time,
        value: item.avgValue, // 使用平均值
        unit: item.unit,
        status: item.status,
        trend: item.trend
      }));

      // 只在图表已初始化时才更新
      if (chart.value) {
        updateChart();
      }
    } else {
      throw new Error(response.msg || "获取数据失败");
    }
  } catch (err) {
    error.value = err.message;
    ElMessage.error("加载图表数据失败: " + err.message);
  } finally {
    loading.value = false;
  }
};

// 更新图表
const updateChart = () => {
  if (
    !chart.value ||
    !chartData.value ||
    !Array.isArray(chartData.value) ||
    chartData.value.length === 0
  ) {
    console.warn("图表或数据未准备好");
    return;
  }

  try {
    // 验证数据格式
    const validData = chartData.value.filter(
      (item) =>
        item &&
        item.time &&
        typeof item.value === "number" &&
        !isNaN(item.value)
    );

    if (validData.length === 0) {
      console.warn("没有有效的图表数据");
      // 设置空数据的完整配置
      chart.value.setOption({
        series: [
          {
            name: getSeriesName(),
            type: "line",
            smooth: true,
            symbol: "circle",
            symbolSize: 4,
            lineStyle: {
              color: getLineColor(),
              width: 2,
            },
            itemStyle: {
              color: getLineColor(),
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: getLineColor() + "40" },
                { offset: 1, color: getLineColor() + "10" },
              ]),
            },
            data: [],
          },
        ],
      });
      return;
    }

    const data = validData.map((item) => [
      new Date(item.time).getTime(),
      Number(item.value.toFixed(2)),
    ]);

    // 使用完整的配置更新图表，而不是部分更新
    chart.value.setOption({
      series: [
        {
          name: getSeriesName(),
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 4,
          lineStyle: {
            color: getLineColor(),
            width: 2,
          },
          itemStyle: {
            color: getLineColor(),
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: getLineColor() + "40" },
              { offset: 1, color: getLineColor() + "10" },
            ]),
          },
          data: data,
        },
      ],
    }, false); // 第二个参数设为false，表示不合并配置，而是替换
  } catch (error) {
    console.warn("更新图表失败:", error);
  }
};



// 处理时间范围变化
const handleTimeRangeChange = () => {
  loadData();
};

// 处理数据类型变化
const handleDataTypeChange = () => {
  // 重新初始化图表以应用新的配置
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }

  nextTick(() => {
    initChart();
    if (chart.value) {
      loadData();
    }
  });
};

// 处理点位ID变化
const handleIdChange = () => {
  // 重新加载数据
  loadData();
};

// 切换实时模式
const toggleRealTime = () => {
  isRealTime.value = !isRealTime.value;

  if (isRealTime.value) {
    startRealTimeUpdate();
  } else {
    stopRealTimeUpdate();
  }
};

// 开始实时更新
const startRealTimeUpdate = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
  }

  realTimeTimer = setInterval(() => {
    loadData();
  }, 5000); // 每5秒更新一次
};

// 停止实时更新
const stopRealTimeUpdate = () => {
  if (realTimeTimer) {
    clearInterval(realTimeTimer);
    realTimeTimer = null;
  }
};

// 重试加载
const retryLoad = () => {
  loadData();
};

// 处理窗口大小变化 - 使用更安全的方式
const handleResize = () => {
  // 清除之前的定时器，实现防抖
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }

  resizeTimer = setTimeout(() => {
    // 完全跳过resize，避免ECharts内部错误
    // 这是一个临时解决方案，直到找到根本原因
    try {
      if (chart.value && typeof chart.value.resize === 'function') {
        // 重新初始化图表而不是resize
        const currentOption = chart.value.getOption();
        if (currentOption && currentOption.series && currentOption.series.length > 0) {
          // 销毁并重新创建图表
          chart.value.dispose();
          chart.value = null;

          nextTick(() => {
            initChart();
            if (chart.value && chartData.value && chartData.value.length > 0) {
              updateChart();
            }
          });
        }
      }
    } catch (error) {
      console.warn("图表resize失败，尝试重新初始化:", error);
      // 如果出错，完全重新初始化
      try {
        if (chart.value) {
          chart.value.dispose();
          chart.value = null;
        }
        nextTick(() => {
          initChart();
          if (chart.value && chartData.value && chartData.value.length > 0) {
            updateChart();
          }
        });
      } catch (reinitError) {
        console.error("重新初始化图表失败:", reinitError);
      }
    }
  }, 300); // 增加延迟时间
};

// 监听 props 变化，同步到内部状态
watch([() => props.dataType, () => props.id], ([newDataType, newId]) => {
  currentDataType.value = newDataType;
  currentId.value = newId;
  // 更新初始状态
  initialDataType.value = newDataType;
  initialId.value = newId;
});

// 监听内部状态变化
watch([currentDataType, currentId], () => {
  try {
    // 停止实时更新
    stopRealTimeUpdate();

    // 移除resize监听器
    window.removeEventListener("resize", handleResize);

    // 销毁现有图表
    if (chart.value && chart.value.dispose) {
      chart.value.dispose();
      chart.value = null;
    }

    nextTick(() => {
      initChart();
      if (chart.value) {
        loadData();
        if (isRealTime.value) {
          startRealTimeUpdate();
        }
      }
    });
  } catch (error) {
    console.warn("重新初始化图表失败:", error);
  }
});

// 组件挂载
onMounted(() => {
  nextTick(() => {
    try {
      // 确保DOM元素已经渲染
      if (!chartRef.value) {
        console.warn("图表容器DOM未准备好，延迟初始化");
        setTimeout(() => {
          if (chartRef.value) {
            initChart();
            if (chart.value) {
              loadData();
              if (isRealTime.value) {
                startRealTimeUpdate();
              }
            }
          }
        }, 100);
        return;
      }

      initChart();
      if (chart.value) {
        loadData();

        if (isRealTime.value) {
          startRealTimeUpdate();
        }
      }
    } catch (error) {
      console.error("组件初始化失败:", error);
      error.value = "图表初始化失败";
    }
  });
});

// 组件卸载
onUnmounted(() => {
  stopRealTimeUpdate();

  try {
    // 清理resize定时器
    if (resizeTimer) {
      clearTimeout(resizeTimer);
      resizeTimer = null;
    }

    // 移除resize监听器
    window.removeEventListener("resize", handleResize);

    // 销毁图表实例
    if (chart.value && chart.value.dispose) {
      chart.value.dispose();
      chart.value = null;
    }
  } catch (error) {
    console.warn("销毁图表失败:", error);
  }
});
</script>

<style scoped>
.monitor-chart-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.chart-header {
  padding: 12px 16px;
  /* background: rgba(0, 162, 255, 0.1); */
  /* border-bottom: 1px solid rgba(0, 162, 255, 0.2); */
}

.chart-title-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.chart-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.chart-left-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-right-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 6px;
}

.control-label {
  color: #ffffff;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.chart-title {
  color: #00a2ff;
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-content {
  position: relative;
}

.chart-wrapper {
  width: 100%;
}

.chart-loading,
.chart-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #ffffff;
  font-size: 14px;
}

.chart-error {
  color: #ff6b6b;
}

.chart-loading .el-icon {
  font-size: 24px;
  color: #00a2ff;
}

.chart-error .el-icon {
  font-size: 24px;
  color: #ff6b6b;
}

/* Element Plus 样式覆盖 */
:deep(.el-select) {
  min-width: 80px;
}

:deep(.el-select .el-input__inner) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 162, 255, 0.3);
  color: #ffffff;
  font-size: 12px;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 162, 255, 0.3);
  box-shadow: 0 0 0 1px rgba(0, 162, 255, 0.3);
}

:deep(.el-select .el-input__wrapper:hover) {
  border-color: rgba(0, 162, 255, 0.5);
  box-shadow: 0 0 0 1px rgba(0, 162, 255, 0.5);
}

:deep(.el-select .el-input__wrapper.is-focus) {
  border-color: #00a2ff;
  box-shadow: 0 0 0 1px #00a2ff;
}

:deep(.el-select .el-input__inner) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-button--small) {
  padding: 5px 8px;
  font-size: 12px;
}

/* 下拉框选项样式 */
:deep(.el-select-dropdown) {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(0, 162, 255, 0.3);
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
  background: transparent;
}

:deep(.el-select-dropdown__item:hover) {
  background: rgba(0, 162, 255, 0.2);
}

:deep(.el-select-dropdown__item.selected) {
  background: rgba(0, 162, 255, 0.3);
  color: #00a2ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-controls-row {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .chart-left-controls,
  .chart-right-controls {
    justify-content: center;
    width: 100%;
  }

  .chart-left-controls {
    order: 2;
  }

  .chart-right-controls {
    order: 1;
  }
}

@media (max-width: 480px) {
  .chart-header {
    padding: 8px 12px;
  }

  .chart-title {
    font-size: 12px;
    margin: 0;
  }

  .chart-left-controls,
  .chart-right-controls {
    flex-wrap: wrap;
    gap: 6px;
  }

  .control-group {
    flex: 1;
    min-width: 0;
  }

  .control-group :deep(.el-select) {
    width: 100% !important;
    min-width: 80px;
  }
}
</style>
