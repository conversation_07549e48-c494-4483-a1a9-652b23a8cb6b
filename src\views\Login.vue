<template>
  <div class="login-container">
    <div class="login-background">
      <div class="tech-grid"></div>
      <div class="tech-particles"></div>
    </div>
    
    <div class="login-box">
      <div class="login-header">
        <h1 class="login-title">青马大桥除湿监测系统</h1>
        <p class="login-subtitle">Bridge Dehumidification Monitoring System</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            class="login-input"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            class="login-input"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-tips">
        <p>默认账号：admin</p>
        <p>默认密码：123456</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    const result = await userStore.login(loginForm.username, loginForm.password)
    
    if (result.success) {
      ElMessage.success(result.message)
      router.push('/dashboard')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    ElMessage.error('登录失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 162, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 162, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(0, 162, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(0, 162, 255, 0.1) 0%, transparent 50%);
}

.login-box {
  position: relative;
  z-index: 2;
  width: 400px;
  margin: 0 auto;
  padding: 40px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(15, 20, 25, 0.9);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #00a2ff;
  margin: 0 0 10px 0;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
}

.login-subtitle {
  font-size: 14px;
  color: #8892b0;
  margin: 0;
  letter-spacing: 1px;
}

.login-form {
  margin-bottom: 20px;
}

.login-input :deep(.el-input__wrapper) {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  box-shadow: none;
  transition: all 0.3s ease;
}

.login-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(0, 162, 255, 0.6);
}

.login-input :deep(.el-input__wrapper.is-focus) {
  border-color: #00a2ff;
  box-shadow: 0 0 0 2px rgba(0, 162, 255, 0.2);
}

.login-input :deep(.el-input__inner) {
  color: #ffffff;
}

.login-input :deep(.el-input__inner::placeholder) {
  color: #8892b0;
}

.login-input :deep(.el-icon) {
  color: #00a2ff;
}

.login-button {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #00a2ff 0%, #0066cc 100%);
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(135deg, #0088dd 0%, #0055aa 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 162, 255, 0.3);
}

.login-tips {
  text-align: center;
  color: #8892b0;
  font-size: 12px;
  line-height: 1.5;
}

.login-tips p {
  margin: 5px 0;
}
</style>
