<template>
  <div class="realtime-monitor-container">
    <div class="monitor-header" v-if="title && title.length > 0">
      <h3 class="monitor-title">{{ title }}</h3>
    </div>

    <div class="monitor-content">
      <!-- <div class="video-box"></div> -->
      <div class="monitor-content value" style="text-align: center;color: #ffffff;display: flex;justify-content: center;">control panel</div>
    </div>

    <!-- 错误提示 -->
    <!-- <div v-if="error" class="error-message">
      <el-icon><Warning /></el-icon>
      <span>{{ error }}</span>
      <el-button @click="reconnect" size="small" type="primary">重连</el-button>
    </div> -->
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "Camera Monitor",
  },
  subTitle: {
    type: String,
    default: "",
  },
  videoUrl: {
    type: String,
    default: "",
  },
});

// CameraMonitor 组件只负责显示摄像头，不需要数据处理逻辑
</script>

<style scoped>
.realtime-monitor-container {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 0px 16px;
  z-index: 10;
  background: rgba(0, 162, 255, 0.04);
  /* border-bottom: 1px solid rgba(0, 162, 255, 0.2); */
}

.monitor-title {
  color: #00a2ff;
  font-size: 24px;
  font-weight: 600;
  margin: 10px auto;
}
.sub-title {
  color: #00a2ff;
  font-size: 20px;
  font-weight: 500;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-icon {
  font-size: 14px;
}

.status-text {
  color: #ffffff;
}

.monitor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 5px 5px;
}
.video-box {
  background: url("@/assets/video-demo.jpg") no-repeat center center;
  background-size: cover;
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.data-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.main-value {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  /* align-items: baseline; */
  position: relative;
}
.main-value .icon {
  font-size: 48px;
  color: #fff;
  /* width: 60px;
  height: 60px;
  background: url("@/assets/icon-temp.svg") no-repeat center center;
  background-size: contain;
  margin-right: 20px; */
}

.value {
  font-size: 38px;
  font-weight: bold;
  color: #00a2ff;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
}

.unit {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.8;
}

.trend-indicator {
  position: absolute;
  top: -8px;
  right: -20px;
}

.trend-icon {
  font-size: 16px;
}

.status-indicators {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.normal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.3);
}

.status-badge.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-badge.danger {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.last-update {
  font-size: 10px;
  color: #ffffff;
  opacity: 0.6;
}

.statistics {
  display: flex;
  justify-content: space-around;
  padding: 15px 0;
  /* border-top: 1px solid rgba(0, 162, 255, 0.2); */
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 16px;
  color: #ffffff;
  opacity: 0.6;
  font-weight: bold;
  margin: 5px auto;
}

.stat-value {
  font-size: 16px;
  color: #00a2ff;
  font-weight: 600;
}

.error-message {
  padding: 12px 16px;
  background: rgba(255, 107, 107, 0.1);
  border-top: 1px solid rgba(255, 107, 107, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  font-size: 12px;
}

.error-message .el-icon {
  font-size: 14px;
}

.error-message .el-button {
  margin-left: auto;
}
</style>
