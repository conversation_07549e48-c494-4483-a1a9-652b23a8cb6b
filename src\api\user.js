/**
 * 用户管理相关 API 接口
 */

import { get, post, put, del } from '@/utils/request'

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 */
export const login = (username, password) => {
  return post('/auth/login', {
    username,
    password
  })
}

/**
 * 用户登出
 */
export const logout = () => {
  return post('/auth/logout')
}

/**
 * 刷新 token
 * @param {string} refreshToken - 刷新令牌
 */
export const refreshToken = (refreshToken) => {
  return post('/auth/refresh', {
    refreshToken
  })
}

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return get('/user/info')
}

/**
 * 更新用户信息
 * @param {object} userInfo - 用户信息
 */
export const updateUserInfo = (userInfo) => {
  return put('/user/info', userInfo)
}

/**
 * 修改密码
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 */
export const changePassword = (oldPassword, newPassword) => {
  return put('/user/password', {
    oldPassword,
    newPassword
  })
}

/**
 * 获取用户列表
 * @param {object} params - 查询参数
 */
export const getUserList = (params = {}) => {
  return get('/user/list', params)
}

/**
 * 添加用户
 * @param {object} userData - 用户数据
 */
export const addUser = (userData) => {
  return post('/user', userData)
}

/**
 * 更新用户
 * @param {string} userId - 用户ID
 * @param {object} userData - 用户数据
 */
export const updateUser = (userId, userData) => {
  return put(`/user/${userId}`, userData)
}

/**
 * 删除用户
 * @param {string} userId - 用户ID
 */
export const deleteUser = (userId) => {
  return del(`/user/${userId}`)
}

/**
 * 重置用户密码
 * @param {string} userId - 用户ID
 * @param {string} newPassword - 新密码
 */
export const resetUserPassword = (userId, newPassword) => {
  return put(`/user/${userId}/password`, {
    newPassword
  })
}

/**
 * 获取用户权限
 * @param {string} userId - 用户ID (可选，不传则获取当前用户权限)
 */
export const getUserPermissions = (userId = null) => {
  const url = userId ? `/user/${userId}/permissions` : '/user/permissions'
  return get(url)
}

/**
 * 更新用户权限
 * @param {string} userId - 用户ID
 * @param {array} permissions - 权限列表
 */
export const updateUserPermissions = (userId, permissions) => {
  return put(`/user/${userId}/permissions`, {
    permissions
  })
}

/**
 * 获取角色列表
 */
export const getRoleList = () => {
  return get('/role/list')
}

/**
 * 获取权限列表
 */
export const getPermissionList = () => {
  return get('/permission/list')
}
