import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd(),"")

  return{
      base: env.VITE_PUBLIC_PATH || "/",
        plugins: [vue()],
        server: {
          port: 5173,
          host: '0.0.0.0',
          open: false,
          proxy: {
              '/dev-api': {
                target: 'http://**************:18001',
                changeOrigin: true,
                rewrite: (p) => p.replace(/^\/dev-api/, '')
              }
            }
        },
        resolve: {
          alias: {
            '@': resolve(__dirname, 'src')
          }
        },
        define: {
          // 修复 SockJS 客户端的全局变量问题
          global: 'globalThis',
        },
        optimizeDeps: {
          include: ['sockjs-client']
        }
  }
  
})
