---
type: "always_apply"
---

# 青马大桥除湿监测系统 - Augment 项目规则

## 项目概述

这是一个基于 Vue3 + Vite + Element Plus + Pinia 架构的蓝色科技风格大屏监测系统，用于青马大桥除湿系统的实时监测和数据展示。

## 技术栈规范

### 核心技术
- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite 5.4.10
- **UI组件库**: Element Plus 2.10.3
- **状态管理**: Pinia 3.0.3
- **路由管理**: Vue Router 4.5.1
- **图表库**: ECharts 5.6.0 + Vue-ECharts 7.0.3
- **图标库**: @element-plus/icons-vue 2.3.1

### 开发环境
- **操作系统**: Windows
- **包管理器**: npm
- **开发服务器**: Vite Dev Server (端口: 5173)
- **终端**: PowerShell

## 项目结构规范

```
qingma-bim-vue/
├── public/                 # 静态资源
├── src/
│   ├── assets/            # 图片、图标等静态资源
│   │   ├── bt_bg.png      # 背景图片
│   │   ├── device-status*.png  # 设备状态图标
│   │   └── icon-temp.*    # 温度图标
│   ├── components/        # 可复用组件
│   │   ├── CameraMonitor.vue      # 摄像头监控组件
│   │   ├── DeviceStatusMonitor.vue # 设备状态监控组件
│   │   ├── IframePanel.vue        # iframe面板组件
│   │   ├── MonitorChart.vue       # 监控图表组件
│   │   ├── RealTimeMonitor.vue    # 实时监控组件
│   │   └── RealTimeMonitorBox.vue # 实时监控盒子组件
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia状态管理
│   │   └── user.js        # 用户状态管理
│   ├── styles/            # 样式文件
│   │   └── global.css     # 全局样式（蓝色科技风格）
│   ├── utils/             # 工具函数
│   │   └── mockData.js    # 模拟数据生成器
│   ├── views/             # 页面组件
│   │   ├── Dashboard.vue  # 主仪表板页面
│   │   ├── Login.vue      # 登录页面
│   │   ├── ChartView.vue  # 图表页面
│   │   └── RealTimeView.vue # 实时监测页面
│   ├── App.vue            # 根组件
│   ├── main.js            # 应用入口
│   └── style.css          # 基础样式
├── package.json           # 项目配置
├── vite.config.js         # Vite配置
└── README.md              # 项目说明
```

## 编码规范

### 1. Vue 组件规范
- **组件命名**: 使用 PascalCase，如 `RealTimeMonitor.vue`
- **组件结构**: 使用 Composition API + `<script setup>`
- **模板语法**: 优先使用 v-if/v-for 等 Vue 指令
- **样式作用域**: 使用 `<style scoped>` 避免样式污染

### 2. JavaScript 规范
- **变量命名**: 使用 camelCase
- **常量命名**: 使用 UPPER_SNAKE_CASE
- **函数命名**: 使用动词开头的 camelCase，如 `handleLogin`
- **导入顺序**: Vue相关 → 第三方库 → 本地组件 → 工具函数

### 3. CSS 规范
- **类命名**: 使用 kebab-case，如 `.dashboard-container`
- **CSS变量**: 使用 `--` 前缀，定义在 `:root` 中
- **响应式**: 使用媒体查询，断点: 768px, 1200px, 1400px
- **动画**: 使用 CSS3 动画，定义在 global.css 中

### 4. 文件命名规范
- **组件文件**: PascalCase.vue
- **页面文件**: PascalCase.vue (放在 views/ 目录)
- **工具文件**: camelCase.js
- **样式文件**: kebab-case.css

## 设计系统规范

### 1. 色彩规范
```css
:root {
  --primary-color: #00a2ff;        /* 主色调 - 蓝色 */
  --primary-light: #33b5ff;        /* 浅蓝色 */
  --primary-dark: #0088cc;         /* 深蓝色 */
  --secondary-color: #1a2332;      /* 次要色 */
  --background-dark: #0c1426;      /* 深色背景 */
  --background-light: #1a2332;     /* 浅色背景 */
  --text-primary: #ffffff;         /* 主文字色 */
  --text-secondary: #8892b0;       /* 次要文字色 */
  --border-color: rgba(0, 162, 255, 0.3);  /* 边框色 */
  --success-color: #00ff88;        /* 成功色 */
  --warning-color: #ffaa00;        /* 警告色 */
  --error-color: #ff4757;          /* 错误色 */
}
```

### 2. 布局规范
- **大屏布局**: 使用 CSS Grid 进行响应式布局
- **组件间距**: 使用 10px, 15px, 20px 的倍数
- **圆角**: 统一使用 6px, 8px
- **阴影**: 使用 `box-shadow` 配合主色调

### 3. 动画规范
- **过渡时间**: 0.3s (快速), 0.6s (正常), 1s (慢速)
- **缓动函数**: ease, ease-out, ease-in-out
- **科技感动画**: glow, pulse, slideIn 等

## 功能模块规范

### 1. 路由配置
```javascript
// 路由参数规范
/realtime/:id?
// id: 点位ID (1-4),每个站点都有4个数据，湿度、温度、气量、压力
// 1: Exhaust Sleeve（No. 49）  站点名称
// 2: Injection Sleeve（No. 57） 站点名称
// 3: Plant Room（No. 61） 站点名称
// 4: Exhaust Sleeve（No. 66） 站点名称
```

### 2. 状态管理
- **用户状态**: 使用 Pinia store 管理登录状态
- **本地存储**: token 和 userInfo 持久化
- **默认账号**: admin / 123456

### 3. 数据模拟
- **实时数据**: 使用 mockData.js 生成模拟数据
- **图表数据**: ECharts 配置，支持实时更新
- **阈值配置**: 可配置的警告和危险阈值

## 组件开发规范

### 1. 监控组件
- **RealTimeMonitor**: 实时数据显示组件
- **MonitorChart**: 图表显示组件
- **CameraMonitor**: 摄像头监控组件
- **DeviceStatusMonitor**: 设备状态组件

### 2. 通用组件
- **IframePanel**: 可配置的iframe组件
- 支持刷新、错误处理、演示模式

### 3. 组件通信
- **Props**: 父子组件数据传递
- **Emit**: 子组件向父组件发送事件
- **Router**: 页面间导航和参数传递

## 性能优化规范

### 1. 代码分割
- 路由懒加载: `() => import('@/views/Dashboard.vue')`
- 组件按需引入

### 2. 资源优化
- 图片压缩和格式优化
- CSS/JS 文件压缩
- 静态资源缓存

### 3. 运行时优化
- 使用 `v-show` 替代频繁切换的 `v-if`
- 合理使用 `computed` 和 `watch`
- 避免在模板中使用复杂表达式

## 测试规范

### 1. 组件测试
- 单元测试覆盖核心组件
- 测试用户交互和数据流
- 模拟API调用和错误处理

### 2. 集成测试
- 路由导航测试
- 状态管理测试
- 端到端用户流程测试

## 部署规范

### 1. 构建配置
```bash
npm run build  # 生产环境构建
npm run preview # 预览构建结果
```

### 2. 环境配置
- 开发环境: `npm run dev`
- 生产环境: 静态文件部署
- 端口配置: 5173 (开发), 可配置 (生产)

## 维护规范

### 1. 版本管理
- 使用语义化版本号
- 及时更新依赖包
- 保持代码风格一致

### 2. 文档维护
- 更新 README.md
- 组件文档和API说明
- 变更日志记录

### 3. 代码质量
- 定期代码审查
- 性能监控和优化
- 安全漏洞检查

## 扩展指南

### 1. 新增监控类型
1. 在 `mockData.js` 中添加数据生成器
2. 创建对应的监控组件
3. 更新路由配置和阈值设置
4. 添加到主仪表板布局

### 2. 新增页面
1. 在 `views/` 目录创建页面组件
2. 配置路由和权限
3. 更新导航菜单
4. 添加页面级样式

### 3. 集成外部系统
1. 使用 IframePanel 组件
2. 配置 URL 和刷新策略
3. 处理跨域和安全问题
4. 添加错误处理机制

## AI 交互指南
- **响应语言**: 中文。
- **任务理解**: 仔细阅读用户需求，确保理解迁移的核心目标和技术选型。
- **代码生成**: 生成的代码需严格遵循上述代码规范与风格。
- **问题处理**: 遇到不明确的需求或技术难题时，应主动向用户提问或寻求澄清。
- **调试信息**: 不要启动项目，我已手工启动。可以使用mcp来获取相关信息。
