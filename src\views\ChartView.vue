<template>
  <div class="chart-view-container">
    <!-- 页面头部 -->
    <div class="chart-header">
      <div class="header-left">
        <h1 class="page-title">数据图表分析</h1>
        <div class="breadcrumb">
          <span>监测系统</span>
          <el-icon><ArrowRight /></el-icon>
          <span>数据图表</span>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button-group>
          <el-button 
            :type="viewMode === 'grid' ? 'primary' : ''"
            @click="viewMode = 'grid'"
            size="small"
          >
            <el-icon><Grid /></el-icon>
            网格视图
          </el-button>
          <el-button 
            :type="viewMode === 'tabs' ? 'primary' : ''"
            @click="viewMode = 'tabs'"
            size="small"
          >
            <el-icon><Menu /></el-icon>
            标签视图
          </el-button>
        </el-button-group>
        
        <el-button @click="refreshAllCharts" size="small" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        
        <el-button @click="exportData" size="small">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 网格视图 -->
    <div v-if="viewMode === 'grid'" class="grid-view">
      <div class="chart-grid">
        <!-- 点位1湿度图表 -->
        <div class="chart-item">
          <MonitorChart
            data-type="humidity"
            id="1"
            :chart-height="280"
            :real-time-enabled="true"
          />
        </div>

        <!-- 点位2湿度图表 -->
        <div class="chart-item">
          <MonitorChart
            data-type="humidity"
            id="2"
            :chart-height="280"
            :real-time-enabled="true"
          />
        </div>

        <!-- 点位3湿度图表 -->
        <div class="chart-item">
          <MonitorChart
            data-type="humidity"
            id="3"
            :chart-height="280"
            :real-time-enabled="true"
          />
        </div>

        <!-- 点位4压力图表 -->
        <div class="chart-item">
          <MonitorChart
            data-type="pressure"
            id="4"
            :chart-height="280"
            :real-time-enabled="true"
          />
        </div>
      </div>

      <!-- 对比分析区域 -->
      <div class="comparison-section">
        <div class="section-header">
          <h3>对比分析</h3>
          <el-select v-model="comparisonType" size="small">
            <el-option label="湿度对比" value="humidity" />
            <el-option label="压力对比" value="pressure" />
            <el-option label="温度对比" value="temperature" />
            <el-option label="流量对比" value="flow" />
          </el-select>
        </div>
        
        <div class="comparison-chart">
          <MonitorChart
            :title="getComparisonTitle"
            :data-type="comparisonType"
            id="1"
            :chart-height="350"
            :real-time-enabled="true"
            key="comparison-chart"
          />
        </div>
      </div>
    </div>

    <!-- 标签视图 -->
    <div v-else class="tabs-view">
      <el-tabs v-model="activeTab" type="border-card" class="chart-tabs">
        <el-tab-pane label="湿度监测" name="humidity">
          <div class="tab-content">
            <div class="charts-row">
              <div class="chart-half">
                <MonitorChart
                  data-type="humidity"
                  id="1"
                  :chart-height="400"
                  :real-time-enabled="true"
                />
              </div>
              <div class="chart-half">
                <MonitorChart
                  data-type="humidity"
                  id="2"
                  :chart-height="400"
                  :real-time-enabled="true"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="压力监测" name="pressure">
          <div class="tab-content">
            <div class="charts-row">
              <div class="chart-half">
                <MonitorChart
                  data-type="pressure"
                  id="3"
                  :chart-height="400"
                  :real-time-enabled="true"
                />
              </div>
              <div class="chart-half">
                <MonitorChart
                  data-type="pressure"
                  id="4"
                  :chart-height="400"
                  :real-time-enabled="true"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="综合分析" name="analysis">
          <div class="tab-content">
            <div class="analysis-grid">
              <div class="analysis-item">
                <MonitorChart
                  title="湿度综合对比"
                  data-type="humidity"
                  id="1"
                  :chart-height="300"
                  :real-time-enabled="true"
                />
              </div>
              <div class="analysis-item">
                <MonitorChart
                  title="压力综合对比"
                  data-type="pressure"
                  id="1"
                  :chart-height="300"
                  :real-time-enabled="true"
                />
              </div>
            </div>
            
            <!-- 数据统计表格 -->
            <div class="statistics-table">
              <h4>数据统计摘要</h4>
              <el-table :data="statisticsData" stripe>
                <el-table-column prop="metric" label="指标" width="120" />
                <el-table-column prop="airHumidity" label="进气湿度 (%)" />
                <el-table-column prop="exhaustHumidity" label="排气湿度 (%)" />
                <el-table-column prop="airPressure" label="进气压力 (hPa)" />
                <el-table-column prop="exhaustPressure" label="排气压力 (hPa)" />
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  ArrowRight, 
  Grid, 
  Menu, 
  Refresh, 
  Download, 
  Loading 
} from '@element-plus/icons-vue'
import MonitorChart from '@/components/MonitorChart.vue'
import { getRealTimeSnapshot } from '@/utils/dataService'

// 响应式数据
const viewMode = ref('grid') // grid, tabs
const activeTab = ref('humidity')
const comparisonType = ref('humidity')
const loading = ref(false)

// 统计数据
const statisticsData = ref([
  {
    metric: '当前值',
    airHumidity: '--',
    exhaustHumidity: '--',
    airPressure: '--',
    exhaustPressure: '--'
  },
  {
    metric: '最大值',
    airHumidity: '--',
    exhaustHumidity: '--',
    airPressure: '--',
    exhaustPressure: '--'
  },
  {
    metric: '最小值',
    airHumidity: '--',
    exhaustHumidity: '--',
    airPressure: '--',
    exhaustPressure: '--'
  },
  {
    metric: '平均值',
    airHumidity: '--',
    exhaustHumidity: '--',
    airPressure: '--',
    exhaustPressure: '--'
  }
])

// 计算属性
const getComparisonTitle = computed(() => {
  return comparisonType.value === 'humidity' ? '湿度对比分析' : '压力对比分析'
})

// 刷新所有图表
const refreshAllCharts = async () => {
  loading.value = true
  
  try {
    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里可以触发所有图表组件的刷新
    // 通过事件总线或者其他方式通知子组件刷新
    
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 导出数据
const exportData = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要导出当前显示的图表数据吗？',
      '导出确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 模拟导出过程
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 这里实现实际的数据导出逻辑
    // 可以导出为Excel、CSV等格式
    
    ElMessage.success('数据导出成功')
  } catch {
    // 用户取消
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatisticsData = async () => {
  try {
    // 获取各类型的实时数据快照
    const snapshot = await getRealTimeSnapshot()
    
    if (snapshot.success) {
      const data = snapshot.data
      
      // 更新当前值
      statisticsData.value[0] = {
        metric: '当前值',
        airHumidity: data.airHumidity.value.toFixed(1),
        exhaustHumidity: data.exhaustHumidity.value.toFixed(1),
        airPressure: data.airPressure.value.toFixed(1),
        exhaustPressure: data.exhaustPressure.value.toFixed(1)
      }
      
      // 这里可以继续获取历史数据来计算最大值、最小值、平均值
      // 为了简化，这里使用模拟数据
      statisticsData.value[1] = {
        metric: '最大值',
        airHumidity: (data.airHumidity.value + 10).toFixed(1),
        exhaustHumidity: (data.exhaustHumidity.value + 8).toFixed(1),
        airPressure: (data.airPressure.value + 5).toFixed(1),
        exhaustPressure: (data.exhaustPressure.value + 3).toFixed(1)
      }
      
      statisticsData.value[2] = {
        metric: '最小值',
        airHumidity: (data.airHumidity.value - 10).toFixed(1),
        exhaustHumidity: (data.exhaustHumidity.value - 8).toFixed(1),
        airPressure: (data.airPressure.value - 5).toFixed(1),
        exhaustPressure: (data.exhaustPressure.value - 3).toFixed(1)
      }
      
      statisticsData.value[3] = {
        metric: '平均值',
        airHumidity: data.airHumidity.value.toFixed(1),
        exhaustHumidity: data.exhaustHumidity.value.toFixed(1),
        airPressure: data.airPressure.value.toFixed(1),
        exhaustPressure: data.exhaustPressure.value.toFixed(1)
      }
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 组件挂载
onMounted(() => {
  loadStatisticsData()
  
  // 定期更新统计数据
  setInterval(loadStatisticsData, 30000) // 每30秒更新一次
})
</script>

<style scoped>
.chart-view-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid rgba(0, 162, 255, 0.3);
  backdrop-filter: blur(10px);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #00a2ff;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #ffffff;
  opacity: 0.7;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.grid-view {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.chart-item {
  min-height: 320px;
}

.comparison-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  color: #00a2ff;
  font-size: 18px;
  margin: 0;
}

.comparison-chart {
  min-height: 390px;
}

.tabs-view {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.chart-tabs {
  height: 100%;
  background: transparent;
  border: 1px solid rgba(0, 162, 255, 0.3);
}

.tab-content {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.charts-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.chart-half {
  min-height: 440px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.analysis-item {
  min-height: 340px;
}

.statistics-table {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 20px;
}

.statistics-table h4 {
  color: #00a2ff;
  margin: 0 0 16px 0;
  font-size: 16px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  color: #ffffff;
  font-size: 16px;
  z-index: 1000;
}

.loading-overlay .el-icon {
  font-size: 32px;
  color: #00a2ff;
}

/* Element Plus 样式覆盖 */
:deep(.el-tabs__header) {
  background: rgba(0, 162, 255, 0.1);
  border-bottom: 1px solid rgba(0, 162, 255, 0.3);
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__item) {
  color: #ffffff;
  border-color: rgba(0, 162, 255, 0.3);
}

:deep(.el-tabs__item.is-active) {
  color: #00a2ff;
  background: rgba(0, 162, 255, 0.1);
}

:deep(.el-table) {
  background: transparent;
  color: #ffffff;
}

:deep(.el-table th) {
  background: rgba(0, 162, 255, 0.1);
  color: #00a2ff;
  border-color: rgba(0, 162, 255, 0.3);
}

:deep(.el-table td) {
  border-color: rgba(0, 162, 255, 0.2);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background: rgba(0, 162, 255, 0.05);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .chart-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-row {
    grid-template-columns: 1fr;
  }
  
  .analysis-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .grid-view,
  .tabs-view {
    padding: 10px;
  }
}
</style>
