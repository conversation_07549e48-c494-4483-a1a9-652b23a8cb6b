/**
 * 设备管理相关 API 接口
 */

import { get, post, put, del } from '@/utils/request'

/**
 * 获取设备状态列表
 * @param {object} params - 查询参数
 */
export const getDeviceStatus = (params = {}) => {
  return get('/device/status', params)
}

/**
 * 获取设备详细信息
 * @param {string} deviceId - 设备ID
 */
export const getDeviceInfo = (deviceId) => {
  return get(`/device/${deviceId}`)
}

/**
 * 获取设备列表
 * @param {object} params - 查询参数
 */
export const getDeviceList = (params = {}) => {
  return get('/device/list', params)
}

/**
 * 添加设备
 * @param {object} deviceData - 设备数据
 */
export const addDevice = (deviceData) => {
  return post('/device', deviceData)
}

/**
 * 更新设备信息
 * @param {string} deviceId - 设备ID
 * @param {object} deviceData - 设备数据
 */
export const updateDevice = (deviceId, deviceData) => {
  return put(`/device/${deviceId}`, deviceData)
}

/**
 * 删除设备
 * @param {string} deviceId - 设备ID
 */
export const deleteDevice = (deviceId) => {
  return del(`/device/${deviceId}`)
}

/**
 * 设备控制
 * @param {string} deviceId - 设备ID
 * @param {string} action - 控制动作 (start, stop, restart, reset)
 * @param {object} params - 控制参数
 */
export const controlDevice = (deviceId, action, params = {}) => {
  return post(`/device/${deviceId}/control`, {
    action,
    params
  })
}

/**
 * 获取设备历史状态
 * @param {string} deviceId - 设备ID
 * @param {object} params - 查询参数
 */
export const getDeviceHistory = (deviceId, params = {}) => {
  return get(`/device/${deviceId}/history`, params)
}

/**
 * 获取设备维护记录
 * @param {string} deviceId - 设备ID
 * @param {object} params - 查询参数
 */
export const getMaintenanceRecords = (deviceId, params = {}) => {
  return get(`/device/${deviceId}/maintenance`, params)
}

/**
 * 添加维护记录
 * @param {string} deviceId - 设备ID
 * @param {object} recordData - 维护记录数据
 */
export const addMaintenanceRecord = (deviceId, recordData) => {
  return post(`/device/${deviceId}/maintenance`, recordData)
}

/**
 * 获取设备配置
 * @param {string} deviceId - 设备ID
 */
export const getDeviceConfig = (deviceId) => {
  return get(`/device/${deviceId}/config`)
}

/**
 * 更新设备配置
 * @param {string} deviceId - 设备ID
 * @param {object} config - 配置数据
 */
export const updateDeviceConfig = (deviceId, config) => {
  return put(`/device/${deviceId}/config`, config)
}

/**
 * 设备诊断
 * @param {string} deviceId - 设备ID
 */
export const diagnoseDevice = (deviceId) => {
  return post(`/device/${deviceId}/diagnose`)
}

/**
 * 获取设备类型列表
 */
export const getDeviceTypes = () => {
  return get('/device/types')
}

/**
 * 批量操作设备
 * @param {array} deviceIds - 设备ID列表
 * @param {string} action - 操作类型
 * @param {object} params - 操作参数
 */
export const batchOperateDevices = (deviceIds, action, params = {}) => {
  return post('/device/batch', {
    deviceIds,
    action,
    params
  })
}
