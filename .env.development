# 开发环境配置
NODE_ENV=development

# 数据源配置 - mock: 使用模拟数据, api: 使用真实API
# VITE_DATA_SOURCE=mock
VITE_DATA_SOURCE=api

# API 基础地址
VITE_API_BASE_URL=/dev-api

# WebSocket 地址 (SockJS需要使用HTTP协议)
VITE_WS_URL=http://192.168.10.101:18001/ws

# 是否启用调试模式
VITE_DEBUG=true

# 应用标题
VITE_APP_TITLE=青马大桥除湿监测系统

# 应用版本
VITE_APP_VERSION=1.0.0

# BIM url
VITE_BIM_URL=http://159.156.2.40:9001/gisBimH5/bimQingmaMaintain.html
