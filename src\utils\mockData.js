/**
 * Mock数据服务
 * 提供模拟的监测数据，包括湿度和压力数据
 */

// 生成随机数据的工具函数
const generateRandomValue = (base, variance) => {
  return base + (Math.random() - 0.5) * variance;
};

// 生成时间序列数据
const generateTimeSeriesData = (hours = 24, interval = 1) => {
  const data = [];
  const now = new Date();

  for (let i = hours; i >= 0; i -= interval) {
    const time = new Date(now.getTime() - i * 60 * 60 * 1000);
    data.push({
      time: time.toISOString(),
      timestamp: time.getTime(),
    });
  }

  return data;
};

// 湿度数据生成器
export const generateHumidityData = (type = "air", hours = 24) => {
  const baseHumidity = type === "air" ? 25 : 25; // 进气湿度较高，排气湿度较低
  const variance = 15;

  return generateTimeSeriesData(hours).map((item) => ({
    ...item,
    value: Math.max(
      0,
      Math.min(100, generateRandomValue(baseHumidity, variance))
    ),
    unit: "%",
    type: type,
    status: "normal", // normal, warning, danger
  }));
};

// 压力数据生成器
export const generatePressureData = (type = "air", hours = 24) => {
  const basePressure = type === "air" ? 2213.25 : 2213.5; // 进气压力略高
  const variance = 5;

  return generateTimeSeriesData(hours).map((item) => ({
    ...item,
    value: generateRandomValue(basePressure, variance),
    unit: "hPa",
    type: type,
    status: "normal",
  }));
};

// 温度数据生成器
export const generateTemperatureData = (type = "air", hours = 24) => {
  const baseTemperature = type === "air" ? 22.5 : 24.8; // 进气温度较低，排气温度较高
  const variance = 3;

  return generateTimeSeriesData(hours).map((item) => ({
    ...item,
    value: generateRandomValue(baseTemperature, variance),
    unit: "℃",
    type: type,
    status: "normal",
  }));
};

// 气量数据生成器
export const generateFlowData = (type = "air", hours = 24) => {
  const baseFlow = type === "air" ? 125.6 : 118.3; // 进气流量较高，排气流量较低
  const variance = 8;

  return generateTimeSeriesData(hours).map((item) => ({
    ...item,
    value: Math.max(0, generateRandomValue(baseFlow, variance)),
    unit: "m³/h",
    type: type,
    status: "normal",
  }));
};

// 实时数据生成器
export const generateRealTimeData = () => {
  const now = new Date();

  return {
    timestamp: now.getTime(),
    time: now.toISOString(),
    // 四个点位的数据，每个点位包含四个数据项
    1: {
      id: '1',
      title: 'Exhaust Sleeve（No. 49）',
      data:{
        humidity: {
          value: generateRandomValue(25, 10),
          unit: "%",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        temperature: {
          value: generateRandomValue(22.5, 3),
          unit: "℃",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        flow: {
          value: generateRandomValue(55.2, 8),
          unit: "m³/h",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        pressure: {
          value: generateRandomValue(2213.25, 3),
          unit: "hPa",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        }
      }
    },
    2: {
      id: '2',
      title: 'Injection Sleeve（No. 57）',
      data:{
        humidity: {
          value: generateRandomValue(45, 8),
          unit: "%",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        temperature: {
          value: generateRandomValue(24.8, 3),
          unit: "℃",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        flow: {
          value: generateRandomValue(55.2, 8),
          unit: "m³/h",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        pressure: {
          value: generateRandomValue(2213.5, 3),
          unit: "hPa",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        }
      }
    },
    3: {
      id: '3',
      title: 'Plant Room（No. 61）',
      data:{ 
        humidity: {
          value: generateRandomValue(35, 12),
          unit: "%",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        temperature: {
          value: generateRandomValue(23.2, 3),
          unit: "℃",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        flow: {
          value: generateRandomValue(55.2, 8),
          unit: "m³/h",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        pressure: {
          value: generateRandomValue(2214.1, 3),
          unit: "hPa",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        }
      }
     
    },
    4: {
      id: '4',
      title: 'Exhaust Sleeve（No. 66）',
      data:{ 
        humidity: {
          value: generateRandomValue(28, 9),
          unit: "%",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        temperature: {
          value: generateRandomValue(25.1, 3),
          unit: "℃",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        flow: {
          value: generateRandomValue(62.8, 8),
          unit: "m³/h",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        },
        pressure: {
          value: generateRandomValue(2212.8, 3),
          unit: "hPa",
          status: "normal",
          trend: Math.random() > 0.5 ? "up" : "down",
        }
      }
    },
  };
};

// Mock HTTP API
export class MockHttpApi {
  constructor() {
    this.baseUrl = "/api/v1";
  }

  // 获取历史数据
  async getHistoryData(type, dataType, hours = 24) {
    // 模拟网络延迟
    await new Promise((resolve) =>
      setTimeout(resolve, 200 + Math.random() * 300)
    );

    if (dataType === "humidity") {
      return {
        success: true,
        data: generateHumidityData(type, hours),
        message: "获取历史湿度数据成功",
      };
    } else if (dataType === "pressure") {
      return {
        success: true,
        data: generatePressureData(type, hours),
        message: "获取历史压力数据成功",
      };
    } else if (dataType === "temperature") {
      return {
        success: true,
        data: generateTemperatureData(type, hours),
        message: "获取历史温度数据成功",
      };
    } else if (dataType === "flow") {
      return {
        success: true,
        data: generateFlowData(type, hours),
        message: "获取历史流量数据成功",
      };
    }

    throw new Error("不支持的数据类型");
  }

  // 获取实时数据快照
  async getRealTimeSnapshot() {
    await new Promise((resolve) =>
      setTimeout(resolve, 100 + Math.random() * 200)
    );

    return {
      success: true,
      data: generateRealTimeData(),
      message: "获取实时数据成功",
    };
  }

  // 获取设备状态
  async getDeviceStatus() {
    await new Promise((resolve) => setTimeout(resolve, 150));

    return {
      success: true,
      data: {
        devices: [
          {
            id: "sensor_air_humidity",
            name: "进气湿度传感器",
            status: "online",
            lastUpdate: new Date().toISOString(),
          },
          {
            id: "sensor_exhaust_humidity",
            name: "排气湿度传感器",
            status: "online",
            lastUpdate: new Date().toISOString(),
          },
          {
            id: "sensor_air_pressure",
            name: "进气压力传感器",
            status: "online",
            lastUpdate: new Date().toISOString(),
          },
          {
            id: "sensor_exhaust_pressure",
            name: "排气压力传感器",
            status: "online",
            lastUpdate: new Date().toISOString(),
          },
        ],
      },
      message: "获取设备状态成功",
    };
  }
}

// Mock WebSocket服务
export class MockWebSocketService {
  constructor() {
    this.listeners = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
  }

  // 连接WebSocket
  connect(url = "ws://localhost:8080/ws") {
    return new Promise((resolve, reject) => {
      try {
        // 模拟WebSocket连接
        setTimeout(() => {
          this.isConnected = true;
          this.reconnectAttempts = 0;

          // 开始发送实时数据
          this.startRealTimeDataStream();

          // 触发连接成功事件
          this.emit("connected", { message: "WebSocket连接成功" });
          resolve();
        }, 500 + Math.random() * 1000);
      } catch (error) {
        reject(error);
      }
    });
  }

  // 断开连接
  disconnect() {
    this.isConnected = false;
    if (this.dataInterval) {
      clearInterval(this.dataInterval);
    }
    this.emit("disconnected", { message: "WebSocket连接已断开" });
  }

  // 开始实时数据流
  startRealTimeDataStream() {
    if (this.dataInterval) {
      clearInterval(this.dataInterval);
    }

    this.dataInterval = setInterval(() => {
      if (this.isConnected) {
        const realTimeData = generateRealTimeData();
        this.emit("realTimeData", realTimeData);
      }
    }, 2000); // 每2秒发送一次数据
  }

  // 事件监听
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // 移除事件监听
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // 触发事件
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error("WebSocket事件处理错误:", error);
        }
      });
    }
  }

  // 模拟重连
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.connect().catch(() => {
          this.reconnect();
        });
      }, this.reconnectInterval);
    } else {
      this.emit("reconnectFailed", { message: "重连失败，已达到最大重试次数" });
    }
  }
}

// 创建全局实例
export const mockHttpApi = new MockHttpApi();
export const mockWebSocket = new MockWebSocketService();

// 默认导出
export default {
  generateHumidityData,
  generatePressureData,
  generateTemperatureData,
  generateFlowData,
  generateRealTimeData,
  MockHttpApi,
  MockWebSocketService,
  mockHttpApi,
  mockWebSocket,
};
