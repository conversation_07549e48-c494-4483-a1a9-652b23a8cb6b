/**
 * 系统管理相关 API 接口
 */

import { get, post, put, del } from '@/utils/request'

/**
 * 获取系统信息
 */
export const getSystemInfo = () => {
  return get('/system/info')
}

/**
 * 获取系统配置
 */
export const getSystemConfig = () => {
  return get('/system/config')
}

/**
 * 更新系统配置
 * @param {object} config - 配置数据
 */
export const updateSystemConfig = (config) => {
  return put('/system/config', config)
}

/**
 * 获取系统日志
 * @param {object} params - 查询参数
 */
export const getSystemLogs = (params = {}) => {
  return get('/system/logs', params)
}

/**
 * 清理系统日志
 * @param {string} logType - 日志类型 (可选)
 * @param {string} beforeDate - 清理指定日期之前的日志
 */
export const clearSystemLogs = (logType = null, beforeDate = null) => {
  return del('/system/logs', {
    data: {
      logType,
      beforeDate
    }
  })
}

/**
 * 获取系统监控数据
 */
export const getSystemMonitor = () => {
  return get('/system/monitor')
}

/**
 * 系统备份
 * @param {object} options - 备份选项
 */
export const backupSystem = (options = {}) => {
  return post('/system/backup', options)
}

/**
 * 获取备份列表
 */
export const getBackupList = () => {
  return get('/system/backup/list')
}

/**
 * 恢复系统
 * @param {string} backupId - 备份ID
 */
export const restoreSystem = (backupId) => {
  return post(`/system/backup/${backupId}/restore`)
}

/**
 * 删除备份
 * @param {string} backupId - 备份ID
 */
export const deleteBackup = (backupId) => {
  return del(`/system/backup/${backupId}`)
}

/**
 * 获取数据字典
 * @param {string} type - 字典类型 (可选)
 */
export const getDictionary = (type = null) => {
  return get('/system/dictionary', {
    type
  })
}

/**
 * 更新数据字典
 * @param {string} type - 字典类型
 * @param {array} items - 字典项
 */
export const updateDictionary = (type, items) => {
  return put('/system/dictionary', {
    type,
    items
  })
}

/**
 * 获取通知公告
 * @param {object} params - 查询参数
 */
export const getNotifications = (params = {}) => {
  return get('/system/notifications', params)
}

/**
 * 发布通知公告
 * @param {object} notification - 通知数据
 */
export const publishNotification = (notification) => {
  return post('/system/notifications', notification)
}

/**
 * 标记通知为已读
 * @param {string} notificationId - 通知ID
 */
export const markNotificationRead = (notificationId) => {
  return put(`/system/notifications/${notificationId}/read`)
}

/**
 * 删除通知
 * @param {string} notificationId - 通知ID
 */
export const deleteNotification = (notificationId) => {
  return del(`/system/notifications/${notificationId}`)
}

/**
 * 文件上传
 * @param {FormData} formData - 文件数据
 */
export const uploadFile = (formData) => {
  return post('/system/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 获取文件列表
 * @param {object} params - 查询参数
 */
export const getFileList = (params = {}) => {
  return get('/system/files', params)
}

/**
 * 删除文件
 * @param {string} fileId - 文件ID
 */
export const deleteFile = (fileId) => {
  return del(`/system/files/${fileId}`)
}
