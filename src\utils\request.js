/**
 * Axios 请求工具类
 * 提供统一的 HTTP 请求封装，包括请求拦截、响应拦截和错误处理
 */

import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'

// 创建 axios 实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    const userStore = useUserStore()
    
    // 添加 token 到请求头
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 调试模式下打印请求信息
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.log('Request:', {
        url: config.url,
        method: config.method,
        params: config.params || {},
        data: config.data || {}
      })
    }
    
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.error('Request Error:', error)
    ElMessage.error('请求发送失败')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    const { data } = response
    
    // 调试模式下打印响应信息
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.log('✅ Response:', {
        url: response.config.url,
        status: response.status,
        data: data
      })
    }
    
    // 统一处理响应格式
    if (data.code !== undefined) {
      // 后端返回的标准格式: { code: 200, data: {}, message: '' }
      if (data.code == 200) {
        return {
          success: true,
          data: data.data,
          message: data.message || '操作成功'
        }
      } else if (data.code == 401) {
        // token 过期或无效
        handleTokenExpired()
        return Promise.reject(new Error(data.message || '登录已过期'))
      } else {
        // 其他业务错误
        ElMessage.error(data.message || '操作失败')
        return Promise.reject(new Error(data.message || '操作失败'))
      }
    }
    
    // 直接返回数据（兼容其他格式）
    return {
      success: true,
      data: data,
      message: '操作成功'
    }
  },
  (error) => {
    // 对响应错误做点什么
    console.error('Response Error:', error)
    
    let message = '网络错误'
    
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data?.message || '请求参数错误'
          break
        case 401:
          message = '登录已过期，请重新登录'
          handleTokenExpired()
          break
        case 403:
          message = '没有权限访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      if (error.code === 'ECONNABORTED') {
        message = '请求超时，请稍后重试'
      } else {
        message = '网络连接失败，请检查网络'
      }
    } else {
      // 其他错误
      message = error.message || '未知错误'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 处理 token 过期
const handleTokenExpired = () => {
  const userStore = useUserStore()
  
  ElMessageBox.confirm(
    '登录状态已过期，请重新登录',
    '提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    userStore.logout()
    // 跳转到登录页
    window.location.href = '/login'
  }).catch(() => {
    // 用户取消
  })
}

// 导出请求方法
export default request

// 导出常用的请求方法
export const get = (url, params = {}, config = {}) => {
  return request({
    method: 'get',
    url,
    params,
    ...config
  })
}

export const post = (url, data = {}, config = {}) => {
  return request({
    method: 'post',
    url,
    data,
    ...config
  })
}

export const put = (url, data = {}, config = {}) => {
  return request({
    method: 'put',
    url,
    data,
    ...config
  })
}

export const del = (url, config = {}) => {
  return request({
    method: 'delete',
    url,
    ...config
  })
}

// 文件上传
export const upload = (url, formData, config = {}) => {
  return request({
    method: 'post',
    url,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 文件下载
export const download = (url, params = {}, filename = '') => {
  return request({
    method: 'get',
    url,
    params,
    responseType: 'blob'
  }).then(response => {
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  })
}
